from datetime import datetime
from enum import IntEnum


class DeviceType(IntEnum):
    WITHINGS = 1
    FITBIT = 2
    DEXCOM = 3


class UnitOfMeasurement(IntEnum):
    NO_UNIT = 1
    KG = 2
    BPM = 3
    MM_HG = 4
    MINUTE = 5
    STEP = 6
    KM = 7
    MI = 8
    LB = 9
    MG_DL = 10


def get_date_from_measure(measure: dict) -> datetime:
    """
    Method to get the date field from a measure dictionary.
    """
    return measure.get("created_at", measure.get("group_date"))
