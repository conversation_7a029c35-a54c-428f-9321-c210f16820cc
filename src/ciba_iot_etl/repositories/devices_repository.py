from typing import List
from uuid import UUID

import pendulum

from ciba_iot_etl.helpers.measurement import DeviceType
from ciba_iot_etl.models.db.member_device import MemberDevice
from ciba_iot_etl.models.pydantic.devices import MemberDeviceData


class MemberDevicesRepository:
    @staticmethod
    async def add_device(member_id: UUID, device_data: MemberDeviceData):
        new_device = MemberDevice(**device_data.model_dump())
        new_device.member_id = member_id

        await new_device.save()

        return new_device

    @staticmethod
    async def get_devices(member_id: UUID):
        return await MemberDevice.filter(member_id=member_id).all()

    @staticmethod
    async def get_devices_by_vendor(member_id: UUID, vendor: DeviceType):
        return await MemberDevice.filter(
            member_id=member_id,
            vendor=vendor,
        ).all()

    @staticmethod
    async def mark_disconnected_devices(member_id: UUID, devices_ids: List[str]):
        current_date = pendulum.now("UTC")

        await MemberDevice.filter(
            member_id=member_id, external_id__in=devices_ids
        ).update(disconnected_at=current_date, disconnected=True)
