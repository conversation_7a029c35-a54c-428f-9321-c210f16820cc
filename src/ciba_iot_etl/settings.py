from pydantic_settings import BaseSettings
from urllib.parse import quote_plus
from enum import Enum
from typing import Any
from ciba_iot_etl.common.aws_handler import get_parameter


class ENV(str, Enum):
    DEV = "dev"
    PROD = "prod"
    LOCAL = "local"
    STG = "stg"
    TEST = "test"
    EPM = "epm"


class IOTSettings(BaseSettings):
    PROJECT_NAME: str = "CibaHealth IoT ETL"
    DEBUG: int = 1
    VERSION: str = "1.0.4"
    ENV: str = ENV.TEST  # Use ENV enum for type

    POSTGRES_USER: str = "admin"
    POSTGRES_PASSWORD: str = "1"
    POSTGRES_DB: str = "rpm_single"
    POSTGRES_HOST: str = "localhost"
    POSTGRES_PORT: int = 5432

    WITHINGS_CLIENT_ID: str = ""
    WITHINGS_CUSTOMER_SECRET: str = ""
    WITHINGS_STATE: str = ""
    WITHINGS_CALLBACK_URI: str = ""
    WITHINGS_NOTIFICATION_CALLBACK_URI: str = ""
    WITHINGS_SCOPE: str = "user.info,user.activity,user.metrics,user.sleepevents"
    WITHINGS_REDIRECT_URI: str = ""

    FITBIT_CLIENT_ID: str = ""
    FITBIT_CLIENT_SECRET: str = ""
    FITBIT_REDIRECT_URI: str = ""
    FITBIT_VERIFY: str = ""
    FITBIT_STATE: str = "state_random"
    FITBIT_VERIFY_SUBSCRIPTION: str = ""
    FITBIT_SCOPE: str = "activity heartrate sleep temperature respiratory_rate cardio_fitness oxygen_saturation"

    DEXCOM_CLIENT_ID: str = ""
    DEXCOM_CLIENT_SECRET: str = ""
    DEXCOM_REDIRECT_URI: str = ""

    SNS_TOPIC_ARN: str = ""
    AWS_BUCKET_NAME: str = ""
    AWS_REGION: str = "us-east-2"

    IS_NEW_ENV: int = 0
    SLACK_SNS_TOPIC_ARN: str = (
        "arn:aws:sns:us-east-2:572827854243:slack-notification-topic"
    )

    @property
    def default_db_url(self) -> str:
        """Construct default database url."""
        return (
            f"postgres://"
            f"{self.POSTGRES_USER}:{quote_plus(self.POSTGRES_PASSWORD)}"
            f"@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}"
            f"/{self.POSTGRES_DB}"
        )


def get_settings(**kwargs: Any) -> IOTSettings:
    """Initialize settings."""
    IOTSettings.model_rebuild()

    # Start with the provided kwargs
    settings_data = kwargs.copy()

    # Create a temporary Settings instance to access ENV and IS_NEW_ENV
    temp_settings = IOTSettings(**settings_data)

    if temp_settings.ENV not in [ENV.LOCAL, ENV.TEST]:
        # Determine the prefix for secrets
        secrets_prefix = "" if temp_settings.IS_NEW_ENV else f"/{temp_settings.ENV}"

        prefix = f"{secrets_prefix}/rpm-single/"

        # List of parameter names to fetch
        parameter_names = [
            "POSTGRES_DB",
            "POSTGRES_USER",
            "POSTGRES_PASSWORD",
            "POSTGRES_HOST",
            "WITHINGS_CLIENT_ID",
            "WITHINGS_CUSTOMER_SECRET",
            "SNS_TOPIC_ARN",
            "FITBIT_CLIENT_ID",
            "FITBIT_CLIENT_SECRET",
            "DEXCOM_CLIENT_ID",
            "DEXCOM_CLIENT_SECRET",
        ]

        # Fetch parameters using get_parameter and store them in a dictionary
        fetched_parameters = {
            name: get_parameter(f"{prefix}{name}") for name in parameter_names
        }

        # Update the settings data with the fetched parameters
        settings_data.update(fetched_parameters)

    # Create the final Settings instance with all the data
    settings = IOTSettings(**settings_data)

    return settings
