from tortoise.models import Model
from tortoise import fields

from ciba_iot_etl.models.db.base import TimestampMixin
from ciba_iot_etl.models.pydantic.transtek import (
    TranstekDeviceType,
    TranstekStatus,
)
from ciba_iot_etl.models.pydantic.common import Carrier


class Transtek(Model, TimestampMixin):
    id = fields.UUIDField(pk=True)
    serial_number = fields.CharField(max_length=12)
    imei = fields.CharField(max_length=15)
    model = fields.Char<PERSON>ield(max_length=20)
    device_type = fields.CharEnumField(TranstekDeviceType, max_length=20)
    tracking_number = fields.Char<PERSON>ield(max_length=40)
    carrier = fields.CharEnumField(Carrier, max_length=20)
    timezone = fields.CharField(max_length=40)
    last_status_report = fields.JSONField()
    status = fields.CharEnumField(TranstekStatus, max_length=20)

    member = fields.ForeignKeyField(
        "models.Member", related_name="transtek_scale", null=True, blank=True
    )

    class Meta:
        table = "transtek_scale"
