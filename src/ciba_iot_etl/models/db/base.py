from typing import Any
from uuid import uuid4

from pypika.terms import Function
from tortoise.expressions import F
from tortoise.fields import (
    <PERSON><PERSON>anField,
    DatetimeField,
    IntEnumField,
    JSONField,
    UUIDField,
)

from ciba_iot_etl.helpers.measurement import DeviceType, UnitOfMeasurement


class JsonbSet(Function):
    def __init__(
        self, field: F, path: str, value: Any, create_if_missing: bool = False
    ):
        super().__init__("jsonb_set", field, path, value, create_if_missing)


class TimestampMixin:
    """Date mixin for database models."""

    created_at = DatetimeField(null=True, auto_now_add=True)
    updated_at = DatetimeField(null=True, auto_now=True)


class StatusMixin:
    """Status mixin for database models."""

    deleted = BooleanField(default=False)


class FieldExistsMixin:
    """Mixin to check if field exists in model."""

    @classmethod
    def field_exists(cls, field_name):
        """
        Method to check if field exists in model.
        """
        return field_name in cls._meta.fields_map


class MeasurementMixin:
    id = UUIDField(primary_key=True, default=uuid4)
    device = IntEnumField(DeviceType)
    unit = IntEnumField(UnitOfMeasurement, default=UnitOfMeasurement.NO_UNIT)
    metadata = JSONField(binary=True, null=True)
