from decimal import Decimal

import pytest

from ciba_iot_etl.helpers.unit_transformation import parse_kg_to_lb


@pytest.mark.parametrize(
    "test_value, number_after_comma, expected_value",
    [
        # Basic Decimal inputs
        (Decimal(100), 2, <PERSON><PERSON><PERSON>("220.46")),
        (<PERSON><PERSON><PERSON>(50), 2, <PERSON><PERSON><PERSON>("110.23")),
        (Dec<PERSON><PERSON>(100.12), 2, <PERSON><PERSON><PERSON>("220.73")),
        (Dec<PERSON><PERSON>(50.34), 2, <PERSON><PERSON><PERSON>("110.98")),
        (Decima<PERSON>("70.5"), 2, <PERSON><PERSON><PERSON>("155.43")),
        (Decimal("75.1"), 2, <PERSON><PERSON><PERSON>("165.57")),
        (Dec<PERSON><PERSON>("75.13"), 2, <PERSON><PERSON><PERSON>("165.63")),
        (Dec<PERSON><PERSON>("75.135"), 2, <PERSON><PERSON><PERSON>("165.64")),
        # Integer inputs
        (100, 2, Decimal("220.46")),
        (50, 2, <PERSON><PERSON><PERSON>("110.23")),
        (0, 2, <PERSON><PERSON><PERSON>("0.00")),
        (1, 2, <PERSON><PERSON><PERSON>("2.20")),
        # Float inputs
        (100.0, 2, <PERSON><PERSON><PERSON>("220.46")),
        (50.0, 2, <PERSON><PERSON><PERSON>("110.23")),
        (0.0, 2, Decimal("0.00")),
        (1.0, 2, Decimal("2.20")),
        (75.135, 2, Decimal("165.64")),
        # Edge cases
        (Decimal("0"), 2, Decimal("0.00")),
        (Decimal("-1"), 2, Decimal("-2.20")),
        (Decimal("-50.5"), 2, Decimal("-111.33")),
        (Decimal("0.0001"), 2, Decimal("0.00")),
        (Decimal("999999.9999"), 2, Decimal("2204620.00")),
        # Custom rounding
        (Decimal("75.135"), 3, Decimal("165.644")),
        (Decimal("75.135"), 0, Decimal("166")),
        (Decimal("75.135"), 1, Decimal("165.6")),
        (Decimal("75.135"), 5, Decimal("165.64412")),
        # Float inputs with custom rounding
        (75.135, 3, Decimal("165.644")),
        (75.135, 0, Decimal("166")),
        (75.135, 1, Decimal("165.6")),
        (75.135, 5, Decimal("165.64412")),
        # Integer inputs with custom rounding
        (75, 3, Decimal("165.347")),
        (75, 0, Decimal("165")),
        (75, 1, Decimal("165.3")),
        (75, 5, Decimal("165.34650")),
    ],
)
def test_parse_kg_to_lb(test_value, number_after_comma, expected_value):
    """
    parse_kg_to_lb should return the provided value multiplied
    by the pounds equivalence (2.20462) and rounded to the specified
    number of decimal places.
    """
    assert parse_kg_to_lb(test_value, number_after_comma) == expected_value, (
        f"Expected {expected_value}, got {parse_kg_to_lb(test_value, number_after_comma)}"
    )
