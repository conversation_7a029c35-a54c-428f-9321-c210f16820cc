from unittest.mock import patch
from datetime import datetime, timedelta
import pytz

from ciba_iot_etl.models.db.withings import Withings

DATETIME_MOCK_PATH = "ciba_iot_etl.utils.datetime_utils.datetime"


class TestWithingsModel:
    """Tests for the Withings model datetime handling."""

    def test_is_access_token_expired_with_naive_datetime(self):
        """Test is_access_token_expired with naive datetime."""
        # Create a Withings instance with a naive datetime
        withings = Withings()
        # Set expiration to 1 hour in the past
        withings.access_token_expires_at = datetime.now() - timedelta(hours=1)

        # Token should be expired
        assert withings.is_access_token_expired() is True

        # Set expiration to 1 hour in the future
        withings.access_token_expires_at = datetime.now() + timedelta(hours=1)

        # Token should not be expired
        assert withings.is_access_token_expired() is False

    def test_is_access_token_expired_with_aware_datetime(self):
        """Test is_access_token_expired with timezone-aware datetime."""
        # Create a Withings instance with a timezone-aware datetime
        withings = Withings()
        now = datetime.now(pytz.UTC)

        with patch(DATETIME_MOCK_PATH) as mock_datetime:
            mock_datetime.now.return_value = now.replace(tzinfo=None)

            withings.access_token_expires_at = now - timedelta(hours=1)

            assert withings.is_access_token_expired() is True

            withings.access_token_expires_at = now + timedelta(hours=1)

            assert withings.is_access_token_expired() is False

    def test_is_refresh_token_expired_with_naive_datetime(self):
        """Test is_refresh_token_expired with naive datetime."""
        # Create a Withings instance with a naive datetime
        withings = Withings()
        # Set expiration to 1 hour in the past
        withings.refresh_token_expires_at = datetime.now() - timedelta(hours=1)

        # Token should be expired
        assert withings.is_refresh_token_expired() is True

        # Set expiration to 1 hour in the future
        withings.refresh_token_expires_at = datetime.now() + timedelta(hours=1)

        # Token should not be expired
        assert withings.is_refresh_token_expired() is False

    def test_is_refresh_token_expired_with_aware_datetime(self):
        """Test is_refresh_token_expired with timezone-aware datetime."""
        # Create a Withings instance with a timezone-aware datetime
        withings = Withings()
        now = datetime.now(pytz.UTC)

        with patch(DATETIME_MOCK_PATH) as mock_datetime:
            mock_datetime.now.return_value = now.replace(tzinfo=None)
            withings.refresh_token_expires_at = now - timedelta(hours=1)

            assert withings.is_refresh_token_expired() is True

            withings.refresh_token_expires_at = now + timedelta(hours=1)

            assert withings.is_refresh_token_expired() is False

    def test_is_old_refresh_token_expired_with_naive_datetime(self):
        """Test is_old_refresh_token_expired with naive datetime."""
        # Create a Withings instance with a naive datetime
        withings = Withings()
        withings.old_refresh_token = "old_token"
        # Set expiration to 1 hour in the past
        withings.old_refresh_token_expires_at = datetime.now() - timedelta(hours=1)

        # Token should be expired
        assert withings.is_old_refresh_token_expired() is True

        # Set expiration to 1 hour in the future
        withings.old_refresh_token_expires_at = datetime.now() + timedelta(hours=1)

        # Token should not be expired
        assert withings.is_old_refresh_token_expired() is False

    def test_is_old_refresh_token_expired_with_aware_datetime(self):
        """Test is_old_refresh_token_expired with timezone-aware datetime."""
        # Create a Withings instance with a timezone-aware datetime
        withings = Withings()
        withings.old_refresh_token = "old_token"
        now = datetime.now(pytz.UTC)

        with patch(DATETIME_MOCK_PATH) as mock_datetime:
            mock_datetime.now.return_value = now.replace(tzinfo=None)
            withings.old_refresh_token_expires_at = now - timedelta(hours=1)

            assert withings.is_old_refresh_token_expired() is True

            withings.old_refresh_token_expires_at = now + timedelta(hours=1)

            assert withings.is_old_refresh_token_expired() is False

    def test_is_access_token_expired_fallback(self):
        """Test is_access_token_expired fallback to expires_in."""
        # Create a Withings instance with no access_token_expires_at
        withings = Withings()
        withings.access_token_expires_at = None
        withings.expires_in = 5400  # 1 hour and a half

        withings.updated_at = datetime.now() - timedelta(hours=2)

        assert withings.is_access_token_expired() is True

        withings.updated_at = datetime.now() - timedelta(hours=1)

        # Token should not be expired
        assert withings.is_access_token_expired() is False

        now = datetime.now(pytz.UTC)

        with patch(DATETIME_MOCK_PATH) as mock_datetime:
            mock_datetime.now.return_value = now.replace(tzinfo=None)
            withings.updated_at = now - timedelta(hours=2)

            assert withings.is_access_token_expired() is True

    def test_update_tokens_timezone_handling(self):
        """Test update_tokens handles timezones correctly."""
        # Mock datetime.now to return a fixed time
        with patch(DATETIME_MOCK_PATH) as mock_datetime:
            now = datetime.now()
            mock_datetime.now.return_value = now

            # Calculate expected timestamps
            expected_access_token_expires_at = now + timedelta(hours=3)
            expected_refresh_token_expires_at = now + timedelta(days=365)
            expected_old_refresh_token_expires_at = now + timedelta(hours=8)

            # Call the method directly
            result = Withings._calculate_expiration_timestamps()

            # Verify that the timestamps are timezone-naive
            assert result["access_token_expires_at"].tzinfo is None
            assert result["refresh_token_expires_at"].tzinfo is None
            assert result["old_refresh_token_expires_at"].tzinfo is None
            assert result["updated_at"].tzinfo is None
            assert expected_access_token_expires_at == result["access_token_expires_at"]
            assert (
                expected_refresh_token_expires_at == result["refresh_token_expires_at"]
            )
            assert (
                expected_old_refresh_token_expires_at
                == result["old_refresh_token_expires_at"]
            )

    def test_expire_old_refresh_token_timezone_handling(self):
        """Test expire_old_refresh_token handles timezones correctly."""
        # Mock datetime.now to return a fixed time
        with patch(DATETIME_MOCK_PATH) as mock_datetime:
            now = datetime.now()
            mock_datetime.now.return_value = now

            # Call the method directly
            result = Withings._calculate_expire_old_refresh_token_timestamps()

            # Verify that the timestamps are timezone-naive
            assert result["old_refresh_token_expires_at"].tzinfo is None
            assert result["updated_at"].tzinfo is None
