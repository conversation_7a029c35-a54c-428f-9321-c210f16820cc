from unittest.mock import AsyncMock, patch

import pytest

from ciba_iot_etl.extract.withings_api.core import WithingsLoader

test_loader = WithingsLoader(
    client_id="test_client_id",
    client_secret="test_client_secret",
    redirect_uri="https://test.com",
    callback_uri="https://test.com/callback",
    notification_callback_uri="https://test.com/notifications",
    scope="test",
)


@pytest.mark.asyncio
async def test_get_user_devices():
    """
    get_user_devices should call the Withings API
    with the correct parameters.
    """
    test_response = {"status": 0, "body": {}}

    with patch(
        "ciba_iot_etl.extract.withings_api.core.WithingsLoader._call_api_with_rate_limit_handling",
        new_callable=AsyncMock,
        return_value=test_response,
    ) as mock_call:
        actual_value = await test_loader.get_user_devices("test_token")

        mock_call.assert_awaited_once_with(
            "https://wbsapi.withings.net/v2/user",
            method="POST",
            headers={"Authorization": "Bearer test_token"},
            payload={"action": "getdevice"},
        )
        assert actual_value == test_response
