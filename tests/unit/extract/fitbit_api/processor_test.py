from datetime import datetime, timezone

import pytest

from ciba_iot_etl.models.db.sleep import Sleep
from ciba_iot_etl.models.pydantic.fitbit import (
    FitbitSleep,
    SleepLevels,
    SleepSummary,
    SleepSummaryValue,
)
from ciba_iot_etl.extract.fitbit_api.processor import FitbitToTortoiseConverter

test_date = datetime(2025, 1, 1, hour=20, minute=0, second=0, tzinfo=timezone.utc)


@pytest.mark.asyncio
async def test_convert_sleep():
    """
    convert_sleep should map the values of a FitbitSleep model to a Sleep model
    """
    test_value = 0
    test_levels = SleepLevels(
        data=[],
        summary=SleepSummary(
            deep=SleepSummaryValue(count=test_value, minutes=test_value),
            light=SleepSummaryValue(count=test_value, minutes=test_value),
            rem=SleepSummaryValue(count=test_value, minutes=test_value),
            wake=SleepSummaryValue(count=test_value, minutes=test_value),
            restless=SleepSummaryValue(count=test_value, minutes=test_value),
            asleep=SleepSummaryValue(count=test_value, minutes=test_value),
            awake=SleepSummaryValue(count=test_value, minutes=test_value),
        ),
    )
    test_record = {
        "dateOfSleep": test_date.isoformat(),
        "duration": test_value,
        "efficiency": test_value,
        "endTime": test_date.isoformat(),
        "infoCode": test_value,
        "isMainSleep": True,
        "levels": test_levels.model_dump(),
        "logId": test_value,
        "logType": "test",
        "minutesAfterWakeup": test_value,
        "minutesAsleep": test_value,
        "minutesAwake": test_value,
        "minutesToFallAsleep": test_value,
        "startTime": test_date.isoformat(),
        "timeInBed": test_value,
        "type": "test",
    }
    test_fitbit_sleep = FitbitSleep.model_validate(test_record)
    converter = FitbitToTortoiseConverter(test_value)
    actual_value = await converter.convert_sleep(test_fitbit_sleep)

    assert isinstance(actual_value[0], Sleep)
    assert actual_value[0].start_time == test_date
    assert actual_value[0].end_time == test_date
    assert actual_value[0].duration == test_fitbit_sleep.duration
    assert actual_value[0].bed_time == test_fitbit_sleep.time_in_bed
    assert actual_value[0].asleep_time == test_fitbit_sleep.minutes_asleep
    assert actual_value[0].deep_time == test_levels.summary.deep.minutes
    assert actual_value[0].deep_count == test_levels.summary.deep.count
    assert actual_value[0].light_time == test_levels.summary.light.minutes
    assert actual_value[0].light_count == test_levels.summary.light.count
    assert actual_value[0].rem_time == test_levels.summary.rem.minutes
    assert actual_value[0].rem_count == test_levels.summary.rem.count
    assert actual_value[0].wake_time == test_levels.summary.wake.minutes
    assert actual_value[0].wake_count == test_levels.summary.wake.count
    assert actual_value[0].efficiency == test_fitbit_sleep.efficiency
    assert actual_value[0].created_at == test_date
