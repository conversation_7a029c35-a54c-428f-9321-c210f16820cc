import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from ciba_iot_etl.extract.fitbit_api.processor import (
    update_fitbit_health_status,
    handle_expired_tokens,
    refresh_fitbit_token,
)
from ciba_iot_etl.models.db.fitbit import Fitbit

# Define constants to avoid duplication
TEST_REASON = "Test reason"
UPDATE_HEALTH_STATUS_PATH = (
    "ciba_iot_etl.extract.fitbit_api.processor.update_fitbit_health_status"
)
HANDLE_EXPIRED_TOKENS_PATH = (
    "ciba_iot_etl.extract.fitbit_api.processor.handle_expired_tokens"
)
LOG_METRIC_PATH = "ciba_iot_etl.extract.fitbit_api.processor.log_metric"


@pytest.mark.asyncio
async def test_update_fitbit_health_status_success():
    """
    update_fitbit_health_status should update the health status when the connection exists
    and the status is different
    """
    # Mock the Fitbit model
    fitbit_id = "test_id"
    mock_connection = MagicMock()
    mock_connection.healthy = False
    mock_connection.user_id = (
        "test_user_id"  # Add user_id to avoid JSON serialization error
    )

    # Mock the log_metric function to avoid JSON serialization issues
    with patch(LOG_METRIC_PATH) as mock_log_metric:
        # Mock the filter and update methods
        with patch.object(Fitbit, "filter") as mock_filter:
            mock_filter.return_value.first = AsyncMock(return_value=mock_connection)
            mock_filter.return_value.update = AsyncMock()

            # Call the function
            result = await update_fitbit_health_status(fitbit_id, True, TEST_REASON)

            # Verify the result
            assert result is True
            # The filter method is called twice in the implementation
            assert mock_filter.call_count == 2
            mock_filter.assert_any_call(id=fitbit_id)
            mock_filter.return_value.update.assert_called_once_with(healthy=True)

            # Verify that log_metric was called
            mock_log_metric.assert_called_once()


@pytest.mark.asyncio
async def test_update_fitbit_health_status_no_update_needed():
    """
    update_fitbit_health_status should not update the health status when the connection exists
    but the status is the same
    """
    # Mock the Fitbit model
    fitbit_id = "test_id"
    mock_connection = MagicMock()
    mock_connection.healthy = True

    # Mock the log_metric function to avoid JSON serialization issues
    with patch(LOG_METRIC_PATH) as mock_log_metric:
        # Mock the filter and update methods
        with patch.object(Fitbit, "filter") as mock_filter:
            mock_filter.return_value.first = AsyncMock(return_value=mock_connection)
            mock_filter.return_value.update = AsyncMock()

            # Call the function
            result = await update_fitbit_health_status(fitbit_id, True, TEST_REASON)

            # Verify the result
            assert result is True
            # Only one call to filter is needed when no update is required
            assert mock_filter.call_count == 1
            mock_filter.assert_called_once_with(id=fitbit_id)
            mock_filter.return_value.update.assert_not_called()

            # Verify that log_metric was not called (no status change)
            mock_log_metric.assert_not_called()


@pytest.mark.asyncio
async def test_update_fitbit_health_status_connection_not_found():
    """
    update_fitbit_health_status should return False when the connection does not exist
    """
    # Mock the Fitbit model
    fitbit_id = "test_id"

    # Mock the log_metric function to avoid JSON serialization issues
    with patch(LOG_METRIC_PATH) as mock_log_metric:
        # Mock the filter method
        with patch.object(Fitbit, "filter") as mock_filter:
            mock_filter.return_value.first = AsyncMock(return_value=None)

            # Call the function
            result = await update_fitbit_health_status(fitbit_id, True, TEST_REASON)

            # Verify the result
            assert result is False
            mock_filter.assert_called_once_with(id=fitbit_id)

            # Verify that log_metric was called for missing connection
            mock_log_metric.assert_called_once()


@pytest.mark.asyncio
async def test_update_fitbit_health_status_exception():
    """
    update_fitbit_health_status should return False when an exception occurs
    """
    # Mock the Fitbit model
    fitbit_id = "test_id"

    # Mock the log_metric function to avoid JSON serialization issues
    with patch(LOG_METRIC_PATH) as mock_log_metric:
        # Mock the filter method to raise an exception
        with patch.object(Fitbit, "filter") as mock_filter:
            mock_filter.side_effect = Exception("Test exception")

            # Call the function
            result = await update_fitbit_health_status(fitbit_id, True, TEST_REASON)

            # Verify the result
            assert result is False
            mock_filter.assert_called_once_with(id=fitbit_id)

            # Verify that log_metric was called for the exception
            mock_log_metric.assert_called_once()


@pytest.mark.asyncio
async def test_handle_expired_tokens_success():
    """
    handle_expired_tokens should mark the connection as unhealthy and return True
    """
    # Mock the Fitbit model
    fitbit_id = "test_id"
    mock_connection = MagicMock()
    mock_connection.user_id = "test_user_id"

    # Mock the log_metric function
    with patch(LOG_METRIC_PATH) as mock_log_metric:
        # Mock the update_fitbit_health_status function
        with patch(UPDATE_HEALTH_STATUS_PATH) as mock_update_status:
            mock_update_status.return_value = True

            # Mock the Fitbit filter method
            with patch.object(Fitbit, "filter") as mock_filter:
                mock_filter.return_value.first = AsyncMock(return_value=mock_connection)

                # Call the function
                result = await handle_expired_tokens(fitbit_id)

                # Verify the result
                assert result is True
                mock_update_status.assert_called_once()
                mock_filter.assert_called_once_with(id=fitbit_id)

                # Verify that log_metric was called for expired tokens
                mock_log_metric.assert_called_once()


@pytest.mark.asyncio
async def test_handle_expired_tokens_connection_not_found():
    """
    handle_expired_tokens should return False when the connection does not exist
    """
    # Mock the Fitbit model
    fitbit_id = "test_id"

    # Mock the log_metric function
    with patch(LOG_METRIC_PATH) as mock_log_metric:
        # Mock the update_fitbit_health_status function
        with patch(UPDATE_HEALTH_STATUS_PATH) as mock_update_status:
            mock_update_status.return_value = True

            # Mock the Fitbit filter method
            with patch.object(Fitbit, "filter") as mock_filter:
                mock_filter.return_value.first = AsyncMock(return_value=None)

                # Call the function
                result = await handle_expired_tokens(fitbit_id)

                # Verify the result
                assert result is False
                mock_update_status.assert_called_once()
                mock_filter.assert_called_once_with(id=fitbit_id)

                # Verify that log_metric was called for missing connection
                mock_log_metric.assert_called_once()


@pytest.mark.asyncio
async def test_refresh_fitbit_token_refresh_token_expired():
    """
    refresh_fitbit_token should return None when the refresh token is expired
    """
    # Mock the Fitbit model
    fitbit_id = "test_id"
    mock_connection = MagicMock()
    mock_connection.is_refresh_token_expired.return_value = True

    # Mock the FitbitLoader
    mock_service = MagicMock()

    # Mock the handle_expired_tokens function
    with patch(HANDLE_EXPIRED_TOKENS_PATH) as mock_handle_expired:
        mock_handle_expired.return_value = True

        # Call the function
        result = await refresh_fitbit_token(mock_service, fitbit_id, mock_connection)

        # Verify the result
        assert result is None
        mock_connection.is_refresh_token_expired.assert_called_once()
        mock_handle_expired.assert_called_once_with(fitbit_id)


@pytest.mark.asyncio
async def test_refresh_fitbit_token_success():
    """
    refresh_fitbit_token should return the new access token when successful
    """
    # Mock the Fitbit model
    fitbit_id = "test_id"
    mock_connection = MagicMock()
    mock_connection.is_refresh_token_expired.return_value = False
    mock_connection.refresh_token = "refresh_token"

    # Mock the RefreshTokenResp
    mock_token_resp = MagicMock()
    mock_token_resp.error = None
    mock_token_resp.access_token = "new_access_token"
    mock_token_resp.refresh_token = "new_refresh_token"
    mock_token_resp.expires_in = 3600

    # Mock the FitbitLoader
    mock_service = MagicMock()
    mock_service.refresh_token = AsyncMock(return_value=mock_token_resp)

    # Mock the Fitbit.update_tokens method
    with patch.object(Fitbit, "update_tokens") as mock_update_tokens:
        mock_updated_connection = MagicMock()
        mock_updated_connection.access_token = "new_access_token"
        mock_update_tokens.return_value = mock_updated_connection

        # Mock the update_fitbit_health_status function
        with patch(UPDATE_HEALTH_STATUS_PATH) as mock_update_status:
            mock_update_status.return_value = True

            # Call the function
            result = await refresh_fitbit_token(
                mock_service, fitbit_id, mock_connection
            )

            # Verify the result
            assert result == "new_access_token"
            mock_connection.is_refresh_token_expired.assert_called_once()
            mock_service.refresh_token.assert_called_once_with("refresh_token")
            mock_update_tokens.assert_called_once_with(
                fitbit_id=fitbit_id,
                access_token="new_access_token",
                refresh_token="new_refresh_token",
                expires_in=3600,
            )
            mock_update_status.assert_called_once_with(
                fitbit_id, True, "Token refresh successful"
            )


@pytest.mark.asyncio
async def test_refresh_fitbit_token_invalid_grant_error():
    """
    refresh_fitbit_token should return None and call handle_expired_tokens when an invalid_grant error occurs
    """
    # Mock the Fitbit model
    fitbit_id = "test_id"
    mock_connection = MagicMock()
    mock_connection.is_refresh_token_expired.return_value = False
    mock_connection.refresh_token = "refresh_token"

    # Mock the RefreshTokenResp
    mock_token_resp = MagicMock()
    mock_token_resp.error = "invalid_grant"

    # Mock the FitbitLoader
    mock_service = MagicMock()
    mock_service.refresh_token = AsyncMock(return_value=mock_token_resp)

    # Mock the handle_expired_tokens function
    with patch(HANDLE_EXPIRED_TOKENS_PATH) as mock_handle_expired:
        mock_handle_expired.return_value = True

        # Call the function
        result = await refresh_fitbit_token(mock_service, fitbit_id, mock_connection)

        # Verify the result
        assert result is None
        mock_connection.is_refresh_token_expired.assert_called_once()
        mock_service.refresh_token.assert_called_once_with("refresh_token")
        mock_handle_expired.assert_called_once_with(fitbit_id)


@pytest.mark.asyncio
async def test_refresh_fitbit_token_other_error():
    """
    refresh_fitbit_token should return None and update health status when another error occurs
    """
    # Mock the Fitbit model
    fitbit_id = "test_id"
    mock_connection = MagicMock()
    mock_connection.is_refresh_token_expired.return_value = False
    mock_connection.refresh_token = "refresh_token"

    # Mock the RefreshTokenResp
    mock_token_resp = MagicMock()
    mock_token_resp.error = "other_error"

    # Mock the FitbitLoader
    mock_service = MagicMock()
    mock_service.refresh_token = AsyncMock(return_value=mock_token_resp)

    # Mock the update_fitbit_health_status function
    with patch(UPDATE_HEALTH_STATUS_PATH) as mock_update_status:
        mock_update_status.return_value = True

        # Call the function
        result = await refresh_fitbit_token(mock_service, fitbit_id, mock_connection)

        # Verify the result
        assert result is None
        mock_connection.is_refresh_token_expired.assert_called_once()
        mock_service.refresh_token.assert_called_once_with("refresh_token")
        mock_update_status.assert_called_once()
