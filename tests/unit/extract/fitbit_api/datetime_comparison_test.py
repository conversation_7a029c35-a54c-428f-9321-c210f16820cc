import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta
import pytz

from ciba_iot_etl.models.db.fitbit import Fitbit
from ciba_iot_etl.extract.fitbit_api.processor import (
    process_user_message,
    refresh_fitbit_token,
)

# Define constants for paths to patch
FITBIT_FILTER_PATH = "ciba_iot_etl.models.db.fitbit.Fitbit.filter"
REFRESH_TOKEN_PATH = "ciba_iot_etl.extract.fitbit_api.processor.refresh_fitbit_token"
FETCH_DATA_PATH = "ciba_iot_etl.extract.fitbit_api.processor.fetch_fitbit_data"
UPDATE_TOKENS_PATH = "ciba_iot_etl.models.db.fitbit.Fitbit.update_tokens"


class TestDatetimeComparison:
    """Tests for datetime comparison handling in the Fitbit processor."""

    @pytest.mark.asyncio
    async def test_process_user_message_with_timezone_aware_dates(self):
        """Test process_user_message with timezone-aware dates in the Fitbit model."""
        # Create a mock Fitbit instance with timezone-aware datetime
        mock_fitbit = MagicMock()
        mock_fitbit.access_token = "test_access_token"
        mock_fitbit.refresh_token = "test_refresh_token"

        # Use a fixed time to avoid test flakiness
        now = datetime.now(pytz.UTC)

        # Set access token to be expired (1 hour ago) with timezone
        mock_fitbit.access_token_expires_at = now - timedelta(hours=1)
        # Set refresh token to be valid (1 year from now) with timezone
        mock_fitbit.refresh_token_expires_at = now + timedelta(days=365)

        # Mock the is_access_token_expired and is_refresh_token_expired methods
        # to use the actual implementation
        mock_fitbit.is_access_token_expired = Fitbit.is_access_token_expired.__get__(
            mock_fitbit
        )
        mock_fitbit.is_refresh_token_expired = Fitbit.is_refresh_token_expired.__get__(
            mock_fitbit
        )

        # Mock the datetime.now() method to return a fixed time
        with patch("ciba_iot_etl.utils.datetime_utils.datetime") as mock_datetime:
            mock_datetime.now.return_value = now.replace(tzinfo=None)

            # Mock the Fitbit.filter method to return our mock Fitbit instance
            with patch(FITBIT_FILTER_PATH) as mock_filter:
                mock_filter.return_value.first = AsyncMock(return_value=mock_fitbit)
                mock_filter.return_value.update = AsyncMock()

                # Mock the refresh_fitbit_token function to return a new access token
                with patch(REFRESH_TOKEN_PATH) as mock_refresh:
                    mock_refresh.return_value = "new_access_token"

                    # Mock the fetch_fitbit_data function to return empty data
                    with patch(FETCH_DATA_PATH) as mock_fetch:
                        mock_fetch.return_value = []

                        # Mock the FitbitLoader
                        mock_service = MagicMock()

                        # Create a mock message
                        mock_message = MagicMock()
                        mock_message.payload = MagicMock()

                        # Call process_user_message
                        # This should not raise any datetime comparison errors
                        await process_user_message(
                            mock_service, "test_fitbit_id", mock_message
                        )

                        # Verify that refresh_fitbit_token was called
                        mock_refresh.assert_called_once()

    @pytest.mark.asyncio
    async def test_refresh_fitbit_token_with_timezone_aware_dates(self):
        """Test refresh_fitbit_token with timezone-aware dates in the Fitbit model."""
        # Create a mock Fitbit instance with timezone-aware datetimes
        mock_fitbit = MagicMock()
        mock_fitbit.refresh_token = "test_refresh_token"

        # Use a fixed time to avoid test flakiness
        now = datetime.now(pytz.UTC)

        # Set refresh token to be valid (1 year from now) with timezone
        mock_fitbit.refresh_token_expires_at = now + timedelta(days=365)

        # Mock the is_refresh_token_expired method to use the actual implementation
        mock_fitbit.is_refresh_token_expired = Fitbit.is_refresh_token_expired.__get__(
            mock_fitbit
        )

        # Mock the datetime.now() method to return a fixed time
        with patch("ciba_iot_etl.models.db.fitbit.datetime") as mock_datetime:
            mock_datetime.now.return_value = now.replace(tzinfo=None)

            # Mock the FitbitLoader
            mock_service = MagicMock()
            mock_service.refresh_token = AsyncMock(
                return_value=MagicMock(
                    error=None,
                    access_token="new_access_token",
                    refresh_token="new_refresh_token",
                    expires_in=28800,
                )
            )

            # Mock the Fitbit.update_tokens method
            with patch(UPDATE_TOKENS_PATH) as mock_update:
                mock_update.return_value = MagicMock(access_token="new_access_token")

                # Mock the update_fitbit_health_status method
                with patch(
                    "ciba_iot_etl.extract.fitbit_api.processor.update_fitbit_health_status"
                ) as mock_health:
                    mock_health.return_value = True

                    # Call refresh_fitbit_token
                    # This should not raise any datetime comparison errors
                    result = await refresh_fitbit_token(
                        mock_service, "test_fitbit_id", mock_fitbit
                    )

                    # Verify the result
                    assert result == "new_access_token"
                    mock_update.assert_called_once()
