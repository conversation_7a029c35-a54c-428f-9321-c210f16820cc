from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
import time

import pendulum
import pytest

from ciba_iot_etl.extract.fitbit_api.processor import fetch_fitbit_data
from ciba_iot_etl.models.pydantic.common import FitbitPayload

test_token = "<TOKEN>"
test_id = "12345wxyz"


@pytest.fixture(scope="function")
def mock_service():
    mock_service = MagicMock()
    mock_service.get_user = AsyncMock(
        return_value={"user": {"distanceUnit": "km", "weightUnit": "kg"}}
    )
    mock_service.get_all_activities = AsyncMock(return_value={"activities": []})
    mock_service.get_steps = AsyncMock(return_value={"activities-steps": []})
    mock_service.get_active_minutes_lightly = AsyncMock(
        return_value={"activities-minutesLightlyActive": []}
    )
    mock_service.get_active_minutes_fairly = AsyncMock(
        return_value={"activities-minutesFairlyActive": []}
    )
    mock_service.get_active_minutes_very = AsyncMock(
        return_value={"activities-minutesVeryActive": []}
    )
    mock_service.get_distance = AsyncMock(return_value={"activities-distance": []})
    mock_service.get_sleep_data = AsyncMock(return_value={"sleep": []})

    return mock_service


class TestDateHandling:
    """Tests for date handling functions in the Fitbit processor."""

    @pytest.mark.asyncio
    async def test_fetch_fitbit_data_with_integer_timestamps(self, mock_service):
        """Test fetch_fitbit_data with integer timestamps."""
        # Create payload with integer timestamps
        now = int(time.time())
        one_week_ago = now - (7 * 24 * 60 * 60)
        payload = FitbitPayload(
            fitbit_id="test_id",
            scope=["activities", "sleep"],
            start_date=one_week_ago,
            end_date=now,
        )

        # Call the function
        result = await fetch_fitbit_data(test_token, mock_service, payload, test_id)

        # Verify the result structure
        assert "activity_measurements" in result
        assert "sleep_measurements" in result
        assert "heart_rate_measurements" in result
        assert "spo2_measurements" in result

        # Verify API calls were made
        mock_service.get_user.assert_called_once()
        mock_service.get_sleep_data.assert_called_once()

    @pytest.mark.asyncio
    @patch("pendulum.from_timestamp")
    @patch("pendulum.now")
    async def test_fetch_fitbit_data_with_date_calculation_error(
        self, mock_from_timestamp, mock_now, mock_service
    ):
        """Test fetch_fitbit_data handles date calculation errors gracefully."""
        # Create payload with valid timestamps, but we'll patch pendulum to simulate an error
        payload = FitbitPayload(
            fitbit_id="test_id",
            scope=["activities", "sleep"],
            start_date=int(time.time()) - 86400,  # Yesterday
            end_date=int(time.time()),
        )

        # Mock pendulum
        mock_from_timestamp.side_effect = ValueError("Invalid timestamp")
        mock_now.return_value = pendulum.parse("2025-04-01")

        # Call the function - should not raise an exception
        result = await fetch_fitbit_data(test_token, mock_service, payload, test_id)

        # Verify the result structure - should still return data
        assert "activity_measurements" in result
        assert "sleep_measurements" in result

        # In our implementation, if there's an error in date calculation,
        # we use a fallback but don't call the API
        assert mock_service.get_user.call_count == 0

    @pytest.mark.asyncio
    @patch("pendulum.now")
    async def test_fetch_fitbit_data_sleep_data_error_handling(
        self, mock_now, mock_service
    ):
        """Test fetch_fitbit_data handles sleep data errors gracefully."""
        # Mock pendulum.now to return a fixed time
        mock_now.return_value = pendulum.datetime(2023, 1, 10)

        # Create payload
        payload = FitbitPayload(
            fitbit_id="test_id",
            scope=["activities", "sleep"],
            start_date=int(time.mktime(datetime(2023, 1, 1).timetuple())),
            end_date=int(time.mktime(datetime(2023, 1, 5).timetuple())),
        )

        # Call the function
        result = await fetch_fitbit_data(test_token, mock_service, payload, test_id)

        # Verify the result structure
        assert "activity_measurements" in result
        assert "sleep_measurements" in result

        # Verify the sleep data is empty or contains what we mocked
        assert len(result["sleep_measurements"]) == 0
