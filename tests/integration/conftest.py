import pytest
from tortoise import Tortoise
from tortoise.contrib.test import (
    _init_db,
    getDBConfig,
)

MODULES = [
    "aerich.models",
    "ciba_iot_etl.models.db.ihealth",
    "ciba_iot_etl.models.db.oruraring",
    "ciba_iot_etl.models.db.blood_pressure",
    "ciba_iot_etl.models.db.dexcom",
    "ciba_iot_etl.models.db.fitbit",
    "ciba_iot_etl.models.db.heart_rate",
    "ciba_iot_etl.models.db.member",
    "ciba_iot_etl.models.db.member_device",
    "ciba_iot_etl.models.db.member_platform",
    "ciba_iot_etl.models.db.member_state",
    "ciba_iot_etl.models.db.weight",
    "ciba_iot_etl.models.db.withings",
    "ciba_iot_etl.models.db.sleep",
    "ciba_iot_etl.models.db.activity",
    "ciba_iot_etl.models.db.glucose_level",
]


@pytest.fixture(scope="session", autouse=True)
def initialize_test_db(request, event_loop):
    config = getDBConfig(
        modules=MODULES,
        app_label="models",
    )

    event_loop.run_until_complete(_init_db(config))

    def shutdown():
        event_loop.run_until_complete(Tortoise._drop_databases())

    request.addfinalizer(shutdown)
