import pendulum
import pytest
from tortoise.exceptions import IntegrityError

from ciba_iot_etl.helpers.measurement import DeviceType
from ciba_iot_etl.models.pydantic.devices import MemberDeviceData
from ciba_iot_etl.repositories.devices_repository import MemberDevicesRepository
from ciba_iot_etl.test_utils import clean_db, add_test_member

test_device_data = MemberDeviceData(
    last_synced_at=pendulum.now(),
    external_id="123abc",
    device_type="scale",
    vendor=DeviceType.WITHINGS,
)


@pytest.mark.asyncio
async def test_device_addition_success():
    """add_device should add a new device in database."""
    await clean_db()

    test_member = await add_test_member()
    actual_value = await MemberDevicesRepository.add_device(
        test_member.id, test_device_data
    )

    assert actual_value.member_id == test_member.id
    assert actual_value.external_id == test_device_data.external_id
    assert actual_value.disconnected is False
    assert actual_value.disconnected_at is None


@pytest.mark.asyncio
async def test_device_addition_with_constraint_error():
    """
    add_device should raise an exception when same external_id
    is registered twice for the same member.
    """
    await clean_db()

    test_member = await add_test_member()
    await MemberDevicesRepository.add_device(test_member.id, test_device_data)

    with pytest.raises(Exception) as expected_error:
        await MemberDevicesRepository.add_device(test_member.id, test_device_data)

    assert expected_error.type is IntegrityError


@pytest.mark.asyncio
async def test_mark_disconnected_devices_success():
    """
    mark_disconnected_devices should only mark as disconnected the provided devices .
    """
    await clean_db()

    test_member = await add_test_member()
    device_1 = await MemberDevicesRepository.add_device(
        test_member.id, test_device_data
    )
    test_data_2 = test_device_data
    test_data_2.external_id = "456ijk"
    device_2 = await MemberDevicesRepository.add_device(test_member.id, test_data_2)
    test_data_3 = test_device_data
    test_data_3.external_id = "789xyz"
    device_3 = await MemberDevicesRepository.add_device(test_member.id, test_data_3)

    await MemberDevicesRepository.mark_disconnected_devices(
        test_member.id, [device_1.external_id, device_3.external_id]
    )

    devices = await MemberDevicesRepository.get_devices(test_member.id)
    disconnected_devices = [device.id for device in devices if device.disconnected]

    assert device_1.id in disconnected_devices
    assert device_3.id in disconnected_devices
    assert device_2.id not in disconnected_devices


@pytest.mark.asyncio
async def test_get_devices_by_vendor_success():
    """
    get_devices_by_vendor should return all devices matching the vendor.
    """
    await clean_db()

    test_member = await add_test_member()
    test_device_1 = await MemberDevicesRepository.add_device(
        test_member.id, test_device_data
    )
    test_data_2 = test_device_data
    test_data_2.external_id = "456ijk"
    test_data_2.vendor = DeviceType.FITBIT
    test_device_2 = await MemberDevicesRepository.add_device(
        test_member.id, test_data_2
    )

    fitbit_devices = await MemberDevicesRepository.get_devices_by_vendor(
        test_member.id, DeviceType.FITBIT
    )
    withings_devices = await MemberDevicesRepository.get_devices_by_vendor(
        test_member.id, DeviceType.WITHINGS
    )

    assert test_device_1 in withings_devices
    assert test_device_1 not in fitbit_devices
    assert test_device_2 in fitbit_devices
    assert test_device_2 not in withings_devices
