from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "withings" ALTER COLUMN "refresh_token" TYPE VARCHAR(305) USING "refresh_token"::VARCHAR(305);
        ALTER TABLE "withings" ALTER COLUMN "access_token" TYPE VARCHAR(305) USING "access_token"::VARCHAR(305);
        ALTER TABLE "oruraring" ALTER COLUMN "refresh_token" TYPE VARCHAR(305) USING "refresh_token"::VARCHAR(305);
        ALTER TABLE "oruraring" ALTER COLUMN "access_token" TYPE VARCHAR(305) USING "access_token"::VARCHAR(305);
        ALTER TABLE "fitbit" ALTER COLUMN "refresh_token" TYPE VARCHAR(305) USING "refresh_token"::VARCHAR(305);
        ALTER TABLE "fitbit" ALTER COLUMN "access_token" TYPE VARCHAR(305) USING "access_token"::VA<PERSON>HA<PERSON>(305);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "fitbit" ALTER COLUMN "refresh_token" TYPE VARCHAR(255) USING "refresh_token"::VARCHAR(255);
        ALTER TABLE "fitbit" ALTER COLUMN "access_token" TYPE VARCHAR(255) USING "access_token"::VARCHAR(255);
        ALTER TABLE "withings" ALTER COLUMN "refresh_token" TYPE VARCHAR(255) USING "refresh_token"::VARCHAR(255);
        ALTER TABLE "withings" ALTER COLUMN "access_token" TYPE VARCHAR(255) USING "access_token"::VARCHAR(255);
        ALTER TABLE "oruraring" ALTER COLUMN "refresh_token" TYPE VARCHAR(255) USING "refresh_token"::VARCHAR(255);
        ALTER TABLE "oruraring" ALTER COLUMN "access_token" TYPE VARCHAR(255) USING "access_token"::VARCHAR(255);"""
