from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "weights" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "device" SMALLINT NOT NULL,
    "unit" SMALLINT NOT NULL  DEFAULT 1,
    "metadata" JSONB,
    "value" DECIMAL(8,2) NOT NULL,
    "member_id" UUID NOT NULL REFERENCES "members" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_weights_created_0d3708" ON "weights" ("created_at");
COMMENT ON COLUMN "weights"."device" IS 'WITHINGS: 1\nFITBIT: 2';
COMMENT ON COLUMN "weights"."unit" IS 'NO_UNIT: 1\nKG: 2\nBPM: 3\nMM_HG: 4';
COMMENT ON TABLE "weights" IS 'Table to store members'' weight measures';
        CREATE TABLE IF NOT EXISTS "heart_rates" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "device" SMALLINT NOT NULL,
    "unit" SMALLINT NOT NULL  DEFAULT 1,
    "metadata" JSONB,
    "value" INT NOT NULL,
    "member_id" UUID NOT NULL REFERENCES "members" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_heart_rates_created_9795bf" ON "heart_rates" ("created_at");
COMMENT ON COLUMN "heart_rates"."device" IS 'WITHINGS: 1\nFITBIT: 2';
COMMENT ON COLUMN "heart_rates"."unit" IS 'NO_UNIT: 1\nKG: 2\nBPM: 3\nMM_HG: 4';
COMMENT ON TABLE "heart_rates" IS 'Table to store members'' heart rate measures';
        CREATE TABLE IF NOT EXISTS "blood_pressures" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "device" SMALLINT NOT NULL,
    "unit" SMALLINT NOT NULL  DEFAULT 1,
    "metadata" JSONB,
    "diastolic_value" INT NOT NULL,
    "systolic_value" INT NOT NULL,
    "member_id" UUID NOT NULL REFERENCES "members" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_blood_press_created_3650b5" ON "blood_pressures" ("created_at");
COMMENT ON COLUMN "blood_pressures"."device" IS 'WITHINGS: 1\nFITBIT: 2';
COMMENT ON COLUMN "blood_pressures"."unit" IS 'NO_UNIT: 1\nKG: 2\nBPM: 3\nMM_HG: 4';
COMMENT ON TABLE "blood_pressures" IS 'Table to store members'' blood pressure measures';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "weights";
        DROP TABLE IF EXISTS "heart_rates";
        DROP TABLE IF EXISTS "blood_pressures";"""
