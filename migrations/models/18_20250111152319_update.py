from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        COMMENT ON COLUMN "blood_pressures"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6
KM: 7
MI: 8
LB: 9';
        COMMENT ON COLUMN "heart_rates"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6
KM: 7
MI: 8
LB: 9';
        COMMENT ON COLUMN "weights"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6
KM: 7
MI: 8
LB: 9';
        COMMENT ON COLUMN "sleep"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6
KM: 7
MI: 8
LB: 9';
        ALTER TABLE "activity" DROP COLUMN "category_value";
        ALTER TABLE "activity" DROP COLUMN "category_unit";
        COMMENT ON COLUMN "activity"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6
KM: 7
MI: 8
LB: 9';
        ALTER TABLE "activity" ALTER COLUMN "activity_category" TYPE SMALLINT USING "activity_category"::SMALLINT;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        COMMENT ON COLUMN "sleep"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6';
        COMMENT ON COLUMN "weights"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6';
        ALTER TABLE "activity" ADD "category_value" INT NOT NULL;
        ALTER TABLE "activity" ADD "category_unit" VARCHAR(1) NOT NULL;
        COMMENT ON COLUMN "activity"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6';
        ALTER TABLE "activity" ALTER COLUMN "activity_category" TYPE VARCHAR(1) USING "activity_category"::VARCHAR(1);
        COMMENT ON COLUMN "heart_rates"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6';
        COMMENT ON COLUMN "blood_pressures"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
MINUTE: 5
STEP: 6';"""
