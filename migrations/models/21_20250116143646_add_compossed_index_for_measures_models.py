from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE INDEX "idx_blood_press_member__cd176b" ON "blood_pressures" ("member_id", "created_at");
        CREATE INDEX "idx_heart_rates_member__df1fdc" ON "heart_rates" ("member_id", "created_at");
        CREATE INDEX "idx_weights_member__805ac2" ON "weights" ("member_id", "created_at");
        CREATE INDEX "idx_sleep_member__512675" ON "sleep" ("member_id", "created_at");
        CREATE INDEX "idx_activity_member__c4dfb7" ON "activity" ("member_id", "created_at");"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP INDEX "idx_blood_press_member__cd176b";
        DROP INDEX "idx_heart_rates_member__df1fdc";
        DROP INDEX "idx_activity_member__c4dfb7";
        DROP INDEX "idx_weights_member__805ac2";
        DROP INDEX "idx_sleep_member__512675";"""
