from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "fitbit" ADD "old_refresh_token_expires_at" TIMESTAMPTZ;
        ALTER TABLE "fitbit" ALTER COLUMN "updated_at" SET NOT NULL;
        ALTER TABLE "fitbit" ALTER COLUMN "expires_in" SET DEFAULT 0;
        ALTER TABLE "fitbit" ALTER COLUMN "created_at" SET NOT NULL;
        ALTER TABLE "withings" ALTER COLUMN "updated_at" SET NOT NULL;
        ALTER TABLE "withings" ALTER COLUMN "expires_in" SET DEFAULT 0;
        ALTER TABLE "withings" ALTER COLUMN "created_at" SET NOT NULL;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "fitbit" DROP COLUMN "old_refresh_token_expires_at";
        ALTER TABLE "fitbit" ALTER COLUMN "updated_at" DROP NOT NULL;
        ALTER TABLE "fitbit" ALTER COLUMN "expires_in" DROP DEFAULT;
        ALTER TABLE "fitbit" ALTER COLUMN "created_at" DROP NOT NULL;
        ALTER TABLE "withings" ALTER COLUMN "updated_at" DROP NOT NULL;
        ALTER TABLE "withings" ALTER COLUMN "expires_in" DROP DEFAULT;
        ALTER TABLE "withings" ALTER COLUMN "created_at" DROP NOT NULL;"""
