from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "dexcom" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "deleted" BOOL NOT NULL  DEFAULT False,
    "id" UUID NOT NULL  PRIMARY KEY,
    "user_id" VARCHAR(255) NOT NULL UNIQUE,
    "access_token" VARCHAR(305) NOT NULL UNIQUE,
    "refresh_token" VARCHAR(305) NOT NULL UNIQUE,
    "expires_in" INT NOT NULL,
    "member_id" UUID NOT NULL,
    "start" BOOL NOT NULL  DEFAULT False
);
COMMENT ON TABLE "dexcom" IS 'Table to store Dexcom access tokens.';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "dexcom";"""
