from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        COMMENT ON COLUMN "blood_pressures"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4';
        ALTER TABLE "dexcom" ALTER COLUMN "access_token" TYPE VARCHAR(756) USING "access_token"::VARCHAR(756);
        ALTER TABLE "dexcom" ALTER COLUMN "refresh_token" TYPE VARCHAR(756) USING "refresh_token"::VARCHAR(756);
        COMMENT ON COLUMN "heart_rates"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4';
        COMMENT ON COLUMN "weights"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4';
        CREATE TABLE IF NOT EXISTS "sleep" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "device" SMALLINT NOT NULL,
    "unit" SMALLINT NOT NULL  DEFAULT 1,
    "metadata" JSONB,
    "start_time" TIMESTAMPTZ NOT NULL,
    "end_time" TIMESTAMPTZ NOT NULL,
    "duration" INT NOT NULL,
    "bed_time" INT NOT NULL,
    "deep_time" INT NOT NULL,
    "deep_count" INT NOT NULL,
    "light_time" INT NOT NULL,
    "light_count" INT NOT NULL,
    "rem_time" INT NOT NULL,
    "rem_count" INT NOT NULL,
    "wake_time" INT NOT NULL,
    "wake_count" INT NOT NULL,
    "efficiency" INT NOT NULL,
    "member_id" UUID NOT NULL REFERENCES "members" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_sleep_created_4bc8ad" ON "sleep" ("created_at");
COMMENT ON COLUMN "sleep"."device" IS 'WITHINGS: 1\nFITBIT: 2';
COMMENT ON COLUMN "sleep"."unit" IS 'NO_UNIT: 1\nKG: 2\nBPM: 3\nMM_HG: 4';
COMMENT ON TABLE "sleep" IS 'Table to store members'' sleep measures';
        CREATE TABLE IF NOT EXISTS "activity" (
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "id" UUID NOT NULL  PRIMARY KEY,
    "device" SMALLINT NOT NULL,
    "unit" SMALLINT NOT NULL  DEFAULT 1,
    "metadata" JSONB,
    "value" VARCHAR(255) NOT NULL,
    "activity_category" VARCHAR(1) NOT NULL,
    "category_value" INT NOT NULL,
    "category_unit" VARCHAR(1) NOT NULL,
    "member_id" UUID NOT NULL REFERENCES "members" ("id") ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "idx_activity_created_5a6493" ON "activity" ("created_at");
COMMENT ON COLUMN "activity"."device" IS 'WITHINGS: 1\nFITBIT: 2';
COMMENT ON COLUMN "activity"."unit" IS 'NO_UNIT: 1\nKG: 2\nBPM: 3\nMM_HG: 4';
COMMENT ON COLUMN "activity"."activity_category" IS 'WALK: 1\nEXERCISE: 2';
COMMENT ON COLUMN "activity"."category_unit" IS 'NO_UNIT: 1\nKG: 2\nBPM: 3\nMM_HG: 4';
COMMENT ON TABLE "activity" IS 'Table to store members'' activity measures';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "dexcom" ALTER COLUMN "access_token" TYPE VARCHAR(1024) USING "access_token"::VARCHAR(1024);
        ALTER TABLE "dexcom" ALTER COLUMN "refresh_token" TYPE VARCHAR(1024) USING "refresh_token"::VARCHAR(1024);
        COMMENT ON COLUMN "weights"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
STEPS: 5
MINUTES: 6
SECONDS: 7';
        COMMENT ON COLUMN "heart_rates"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
STEPS: 5
MINUTES: 6
SECONDS: 7';
        COMMENT ON COLUMN "blood_pressures"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
STEPS: 5
MINUTES: 6
SECONDS: 7';
        DROP TABLE IF EXISTS "activity";
        DROP TABLE IF EXISTS "sleep";"""
