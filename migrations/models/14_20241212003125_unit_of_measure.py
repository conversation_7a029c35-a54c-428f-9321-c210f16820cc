from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        COMMENT ON COLUMN "weights"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
STEPS: 5
MINUTES: 6
SECONDS: 7';
        COMMENT ON COLUMN "heart_rates"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
STEPS: 5
MINUTES: 6
SECONDS: 7';
        COMMENT ON COLUMN "blood_pressures"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
STEPS: 5
MINUTES: 6
SECONDS: 7';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        COMMENT ON COLUMN "weights"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
STEPS: 5
MINUTES: 6';
        COMMENT ON COLUMN "heart_rates"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
STEPS: 5
MINUTES: 6';
        COMMENT ON COLUMN "blood_pressures"."unit" IS 'NO_UNIT: 1
KG: 2
BPM: 3
MM_HG: 4
STEPS: 5
MINUTES: 6';"""
